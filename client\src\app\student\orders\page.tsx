'use client';

import React, { useState, useEffect } from 'react';
import { Package, Calendar, Coins, RefreshCw, ShoppingBag } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { toast } from 'sonner';
import Image from 'next/image';
import Header from '@/app-components/Header';
import Footer from '@/app-components/Footer';
import { isStudentAuthenticated } from '@/lib/utils';
import * as storePurchaseApi from '@/services/storePurchaseApi';

const StudentOrdersPage = () => {
  const [orders, setOrders] = useState<storePurchaseApi.StoreOrder[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Check if student is authenticated
    if (!isStudentAuthenticated()) {
      toast.error('Please login to view your orders');
      window.location.href = '/student/login';
      return;
    }

    loadOrders();
  }, []);

  const loadOrders = async () => {
    try {
      console.log('=== LOADING STUDENT ORDERS ===');
      setLoading(true);
      
      const ordersData = await storePurchaseApi.getMyOrders();
      console.log('Orders loaded successfully:', ordersData);
      
      setOrders(ordersData || []);
      
      if (ordersData && ordersData.length > 0) {
        toast.success(`Loaded ${ordersData.length} orders successfully`);
      }
    } catch (error: any) {
      console.error('=== ORDERS LOADING ERROR ===');
      console.error('Error details:', error);
      
      setOrders([]);
      toast.error(error.message || 'Failed to load orders');
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'COMPLETED':
        return 'bg-green-100 text-green-800';
      case 'PENDING':
        return 'bg-yellow-100 text-yellow-800';
      case 'CANCELLED':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <>
        <Header />
        <div className="container mx-auto p-6 space-y-6">
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-3xl font-bold">My Orders</h1>
              <p className="text-muted-foreground">View your store purchase history</p>
            </div>
            <Skeleton className="h-10 w-32" />
          </div>
          
          <div className="grid gap-4">
            {[1, 2, 3].map((i) => (
              <Card key={i}>
                <CardContent className="p-6">
                  <div className="flex items-center space-x-4">
                    <Skeleton className="h-16 w-16 rounded" />
                    <div className="flex-1 space-y-2">
                      <Skeleton className="h-4 w-48" />
                      <Skeleton className="h-4 w-32" />
                      <Skeleton className="h-4 w-24" />
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
        <Footer />
      </>
    );
  }

  return (
    <>
      <Header />
      <div className="container mx-auto p-6 space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h1 className="text-3xl font-bold text-foreground">My Orders</h1>
            <p className="text-muted-foreground">View your store purchase history</p>
          </div>
          <Button onClick={loadOrders} disabled={loading} variant="outline">
            <RefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            {loading ? 'Loading...' : 'Refresh'}
          </Button>
        </div>

        {/* Orders List */}
        {orders.length === 0 ? (
          <Card>
            <CardContent className="p-12 text-center">
              <ShoppingBag className="w-16 h-16 mx-auto text-muted-foreground mb-4" />
              <h3 className="text-xl font-semibold mb-2">No Orders Found</h3>
              <p className="text-muted-foreground mb-6">
                You haven't made any purchases yet. Visit our store to start shopping!
              </p>
              <Button onClick={() => window.location.href = '/store'}>
                <ShoppingBag className="w-4 h-4 mr-2" />
                Visit Store
              </Button>
            </CardContent>
          </Card>
        ) : (
          <div className="grid gap-4">
            {orders.map((order) => (
              <Card key={order.id} className="hover:shadow-md transition-shadow">
                <CardContent className="p-6">
                  <div className="flex items-start space-x-4">
                    {/* Item Image */}
                    <div className="relative w-16 h-16 rounded-lg overflow-hidden bg-gray-100 flex-shrink-0">
                      {order.item?.image ? (
                        <Image
                          src={order.item.image}
                          alt={order.itemName}
                          fill
                          className="object-cover"
                        />
                      ) : (
                        <div className="w-full h-full flex items-center justify-center">
                          <Package className="w-8 h-8 text-gray-400" />
                        </div>
                      )}
                    </div>

                    {/* Order Details */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-start justify-between">
                        <div>
                          <h3 className="font-semibold text-lg truncate">{order.itemName}</h3>
                          <p className="text-sm text-muted-foreground">
                            Order ID: {order.id.slice(-8)}
                          </p>
                        </div>
                        <Badge className={getStatusColor(order.status)}>
                          {order.status}
                        </Badge>
                      </div>

                      <div className="mt-3 grid grid-cols-1 sm:grid-cols-3 gap-4 text-sm">
                        <div className="flex items-center space-x-2">
                          <Package className="w-4 h-4 text-muted-foreground" />
                          <span>Quantity: {order.quantity}</span>
                        </div>
                        
                        <div className="flex items-center space-x-2">
                          <Coins className="w-4 h-4 text-orange-500" />
                          <span>{order.totalCoins} Coins</span>
                        </div>
                        
                        <div className="flex items-center space-x-2">
                          <Calendar className="w-4 h-4 text-muted-foreground" />
                          <span>{formatDate(order.createdAt)}</span>
                        </div>
                      </div>

                      {order.item?.description && (
                        <p className="mt-2 text-sm text-muted-foreground line-clamp-2">
                          {order.item.description}
                        </p>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        {/* Summary */}
        {orders.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Package className="w-5 h-5" />
                <span>Order Summary</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 text-center">
                <div>
                  <p className="text-2xl font-bold">{orders.length}</p>
                  <p className="text-sm text-muted-foreground">Total Orders</p>
                </div>
                <div>
                  <p className="text-2xl font-bold">
                    {orders.reduce((sum, order) => sum + order.quantity, 0)}
                  </p>
                  <p className="text-sm text-muted-foreground">Items Purchased</p>
                </div>
                <div>
                  <p className="text-2xl font-bold text-orange-600">
                    {orders.reduce((sum, order) => sum + order.totalCoins, 0)}
                  </p>
                  <p className="text-sm text-muted-foreground">Coins Spent</p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
      <Footer />
    </>
  );
};

export default StudentOrdersPage;
