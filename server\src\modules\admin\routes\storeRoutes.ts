import { Router } from 'express';
import * as storeController from '../controllers/storeController';
import * as storeOrderController from '../controllers/storeOrderController';
import { authMiddleware } from '@/middlewares/adminAuth';
import multer from 'multer';
import path from 'path';
import fs from 'fs';

const router = Router();

// Configure multer for store item images
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const dir = path.join('uploads', 'store');
    fs.mkdirSync(dir, { recursive: true });
    cb(null, dir);
  },
  filename: (req, file, cb) => {
    const ext = path.extname(file.originalname);
    const name = path.basename(file.originalname, ext).replace(/\s+/g, '-');
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1e9);
    cb(null, `${name}-${uniqueSuffix}${ext}`);
  },
});

const upload = multer({
  storage,
  limits: { fileSize: 5 * 1024 * 1024 }, // 5MB limit
  fileFilter: (req, file, cb) => {
    if (!file.mimetype.startsWith('image/')) {
      return cb(new Error('Only image files are allowed'));
    }
    cb(null, true);
  },
});

router.get('/', storeController.getAllStoreItems);

router.get('/stats', storeController.getStoreStats);

router.get('/:id', storeController.getStoreItemById);

router.post('/', upload.single('image'), storeController.createStoreItem);

router.put('/:id', authMiddleware, upload.single('image'), storeController.updateStoreItem);

router.delete('/:id', authMiddleware, storeController.deleteStoreItem);

// Store order routes
// Simple test route
router.get('/orders/test', (req, res) => {
  res.json({
    message: 'Store orders route is working!',
    timestamp: new Date().toISOString(),
    server: 'Backend is running properly'
  });
});

// Simple orders route that always works
router.get('/orders/simple', (req, res) => {
  res.json({
    success: true,
    data: [],
    message: 'Simple orders endpoint working'
  });
});

router.get('/orders/raw', async (req, res) => {
  try {
    const prisma = require('@/config/prismaClient').default;
    const rawOrders = await prisma.storeOrder.findMany({
      orderBy: { createdAt: 'desc' },
      take: 10
    });
    res.json({
      message: 'Raw orders (without relationships)',
      count: rawOrders.length,
      orders: rawOrders
    });
  } catch (error: any) {
    res.status(500).json({ error: error.message });
  }
});

router.get('/orders', storeOrderController.getAllOrders);

router.get('/orders/stats', storeOrderController.getOrderStats);

router.get('/orders/:orderId', storeOrderController.getOrderDetails);

router.put('/orders/:orderId', storeOrderController.updateOrder);

export default router;
