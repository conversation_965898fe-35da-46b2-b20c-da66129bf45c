{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/admin/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from 'react';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction Card({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"card\"\r\n      className={cn(\r\n        'bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction CardHeader({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-header\"\r\n      className={cn(\r\n        '@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction CardTitle({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-title\"\r\n      className={cn('leading-none font-semibold', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction CardDescription({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-description\"\r\n      className={cn('text-muted-foreground text-sm', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction CardAction({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-action\"\r\n      className={cn('col-start-2 row-span-2 row-start-1 self-start justify-self-end', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction CardContent({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return <div data-slot=\"card-content\" className={cn('px-6', className)} {...props} />;\r\n}\r\n\r\nfunction CardFooter({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-footer\"\r\n      className={cn('flex items-center px-6 [.border-t]:pt-6', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Card, CardHeader, CardFooter, CardTitle, CardAction, CardDescription, CardContent };\r\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,kEAAkE;QAC/E,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBAAO,8OAAC;QAAI,aAAU;QAAe,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QAAa,GAAG,KAAK;;;;;;AAClF;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 104, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/admin/src/components/ui/table.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as React from 'react';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction Table({ className, ...props }: React.ComponentProps<'table'>) {\r\n  return (\r\n    <div data-slot=\"table-container\" className=\"relative w-full overflow-x-auto\">\r\n      <table\r\n        data-slot=\"table\"\r\n        className={cn('w-full caption-bottom text-sm', className)}\r\n        {...props}\r\n      />\r\n    </div>\r\n  );\r\n}\r\n\r\nfunction TableHeader({ className, ...props }: React.ComponentProps<'thead'>) {\r\n  return <thead data-slot=\"table-header\" className={cn('[&_tr]:border-b', className)} {...props} />;\r\n}\r\n\r\nfunction TableBody({ className, ...props }: React.ComponentProps<'tbody'>) {\r\n  return (\r\n    <tbody\r\n      data-slot=\"table-body\"\r\n      className={cn('[&_tr:last-child]:border-0', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction TableFooter({ className, ...props }: React.ComponentProps<'tfoot'>) {\r\n  return (\r\n    <tfoot\r\n      data-slot=\"table-footer\"\r\n      className={cn('bg-muted/50 border-t font-medium [&>tr]:last:border-b-0', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction TableRow({ className, ...props }: React.ComponentProps<'tr'>) {\r\n  return (\r\n    <tr\r\n      data-slot=\"table-row\"\r\n      className={cn(\r\n        'hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction TableHead({ className, ...props }: React.ComponentProps<'th'>) {\r\n  return (\r\n    <th\r\n      data-slot=\"table-head\"\r\n      className={cn(\r\n        'text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction TableCell({ className, ...props }: React.ComponentProps<'td'>) {\r\n  return (\r\n    <td\r\n      data-slot=\"table-cell\"\r\n      className={cn(\r\n        'p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction TableCaption({ className, ...props }: React.ComponentProps<'caption'>) {\r\n  return (\r\n    <caption\r\n      data-slot=\"table-caption\"\r\n      className={cn('text-muted-foreground mt-4 text-sm', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Table, TableHeader, TableBody, TableFooter, TableHead, TableRow, TableCell, TableCaption };\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAIA;AAJA;;;AAMA,SAAS,MAAM,EAAE,SAAS,EAAE,GAAG,OAAsC;IACnE,qBACE,8OAAC;QAAI,aAAU;QAAkB,WAAU;kBACzC,cAAA,8OAAC;YACC,aAAU;YACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;YAC9C,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBAAO,8OAAC;QAAM,aAAU;QAAe,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,mBAAmB;QAAa,GAAG,KAAK;;;;;;AAC/F;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAsC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2DAA2D;QACxE,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAmC;IACnE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+EACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sJACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAwC;IAC5E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 222, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/admin/src/app-components/dataTable.tsx"], "sourcesContent": ["import {\r\n  useReactTable,\r\n  getCoreRowModel,\r\n  ColumnDef,\r\n  flexRender,\r\n} from \"@tanstack/react-table\";\r\n\r\nimport {\r\n  Table,\r\n  TableBody,\r\n  TableCell,\r\n  TableHead,\r\n  TableHeader,\r\n  TableRow,\r\n} from \"@/components/ui/table\";\r\n\r\ninterface DataTableProps<T> {\r\n  columns: ColumnDef<T>[];\r\n  data: T[];\r\n  isLoading?: boolean;\r\n  getRowClassName?: (row: T, index: number) => string;\r\n}\r\n\r\nexport function DataTable<T>({\r\n  columns,\r\n  data,\r\n  isLoading,\r\n  getRowClassName,\r\n}: DataTableProps<T>) {\r\n  const table = useReactTable({\r\n    data,\r\n    columns,\r\n    getCoreRowModel: getCoreRowModel(),\r\n  });\r\n\r\n  return (\r\n    <div>\r\n      {isLoading ? (\r\n        <div className=\"flex justify-center items-center h-64\">\r\n          <div className=\"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary\"></div>\r\n        </div>\r\n      ) : (\r\n        <div className=\"rounded-md border\">\r\n          <Table>\r\n            <TableHeader className=\"sticky top-0 bg-muted z-10\">\r\n              {table.getHeaderGroups().map((headerGroup) => (\r\n                <TableRow key={headerGroup.id}>\r\n                  {headerGroup.headers.map((header) => (\r\n                    <TableHead key={header.id}>\r\n                      {header.isPlaceholder\r\n                        ? null\r\n                        : flexRender(\r\n                            header.column.columnDef.header,\r\n                            header.getContext()\r\n                          )}\r\n                    </TableHead>\r\n                  ))}\r\n                </TableRow>\r\n              ))}\r\n            </TableHeader>\r\n            <TableBody>\r\n              {table.getRowModel().rows?.length ? (\r\n                table.getRowModel().rows.map((row, index) => {\r\n                  const customClassName = getRowClassName\r\n                    ? getRowClassName(row.original, index)\r\n                    : \"\";\r\n                  return (\r\n                    <TableRow\r\n                      key={row.id}\r\n                      className={`hover:bg-gray-50 ${customClassName}`}\r\n                    >\r\n                      {row.getVisibleCells().map((cell) => (\r\n                        <TableCell key={cell.id}>\r\n                          {flexRender(\r\n                            cell.column.columnDef.cell,\r\n                            cell.getContext()\r\n                          )}\r\n                        </TableCell>\r\n                      ))}\r\n                    </TableRow>\r\n                  );\r\n                })\r\n              ) : (\r\n                <TableRow>\r\n                  <TableCell\r\n                    colSpan={columns.length}\r\n                    className=\"text-center py-4\"\r\n                  >\r\n                    No data found.\r\n                  </TableCell>\r\n                </TableRow>\r\n              )}\r\n            </TableBody>\r\n          </Table>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AAOA;;;;AAgBO,SAAS,UAAa,EAC3B,OAAO,EACP,IAAI,EACJ,SAAS,EACT,eAAe,EACG;IAClB,MAAM,QAAQ,CAAA,GAAA,sLAAA,CAAA,gBAAa,AAAD,EAAE;QAC1B;QACA;QACA,iBAAiB,CAAA,GAAA,qKAAA,CAAA,kBAAe,AAAD;IACjC;IAEA,qBACE,8OAAC;kBACE,0BACC,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;;;;;;;;;iCAGjB,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;;kCACJ,8OAAC,iIAAA,CAAA,cAAW;wBAAC,WAAU;kCACpB,MAAM,eAAe,GAAG,GAAG,CAAC,CAAC,4BAC5B,8OAAC,iIAAA,CAAA,WAAQ;0CACN,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,uBACxB,8OAAC,iIAAA,CAAA,YAAS;kDACP,OAAO,aAAa,GACjB,OACA,CAAA,GAAA,sLAAA,CAAA,aAAU,AAAD,EACP,OAAO,MAAM,CAAC,SAAS,CAAC,MAAM,EAC9B,OAAO,UAAU;uCALT,OAAO,EAAE;;;;;+BAFd,YAAY,EAAE;;;;;;;;;;kCAcjC,8OAAC,iIAAA,CAAA,YAAS;kCACP,MAAM,WAAW,GAAG,IAAI,EAAE,SACzB,MAAM,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK;4BACjC,MAAM,kBAAkB,kBACpB,gBAAgB,IAAI,QAAQ,EAAE,SAC9B;4BACJ,qBACE,8OAAC,iIAAA,CAAA,WAAQ;gCAEP,WAAW,CAAC,iBAAiB,EAAE,iBAAiB;0CAE/C,IAAI,eAAe,GAAG,GAAG,CAAC,CAAC,qBAC1B,8OAAC,iIAAA,CAAA,YAAS;kDACP,CAAA,GAAA,sLAAA,CAAA,aAAU,AAAD,EACR,KAAK,MAAM,CAAC,SAAS,CAAC,IAAI,EAC1B,KAAK,UAAU;uCAHH,KAAK,EAAE;;;;;+BAJpB,IAAI,EAAE;;;;;wBAajB,mBAEA,8OAAC,iIAAA,CAAA,WAAQ;sCACP,cAAA,8OAAC,iIAAA,CAAA,YAAS;gCACR,SAAS,QAAQ,MAAM;gCACvB,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWnB", "debugId": null}}, {"offset": {"line": 336, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/admin/src/components/ui/select.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as React from 'react';\r\nimport * as SelectPrimitive from '@radix-ui/react-select';\r\nimport { CheckIcon, ChevronDownIcon, ChevronUpIcon } from 'lucide-react';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction Select({ ...props }: React.ComponentProps<typeof SelectPrimitive.Root>) {\r\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />;\r\n}\r\n\r\nfunction SelectGroup({ ...props }: React.ComponentProps<typeof SelectPrimitive.Group>) {\r\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />;\r\n}\r\n\r\nfunction SelectValue({ ...props }: React.ComponentProps<typeof SelectPrimitive.Value>) {\r\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />;\r\n}\r\n\r\nfunction SelectTrigger({\r\n  className,\r\n  size = 'default',\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\r\n  size?: 'sm' | 'default';\r\n}) {\r\n  return (\r\n    <SelectPrimitive.Trigger\r\n      data-slot=\"select-trigger\"\r\n      data-size={size}\r\n      className={cn(\r\n        \"border-input mt-2 data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-full items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <div className=\"flex-1 text-left\">\r\n        {children}\r\n      </div>\r\n      <SelectPrimitive.Icon asChild>\r\n        <ChevronDownIcon className=\"size-4 opacity-50 ml-2\" />\r\n      </SelectPrimitive.Icon>\r\n    </SelectPrimitive.Trigger>\r\n  );\r\n}\r\n\r\nfunction SelectContent({\r\n  className,\r\n  children,\r\n  position = 'popper',\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\r\n  return (\r\n    <SelectPrimitive.Portal>\r\n      <SelectPrimitive.Content\r\n        data-slot=\"select-content\"\r\n        className={cn(\r\n          'bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md',\r\n          position === 'popper' &&\r\n            'data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1',\r\n          className\r\n        )}\r\n        position={position}\r\n        {...props}\r\n      >\r\n        <SelectScrollUpButton />\r\n        <SelectPrimitive.Viewport\r\n          className={cn(\r\n            'p-1',\r\n            position === 'popper' &&\r\n              'h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1'\r\n          )}\r\n        >\r\n          {children}\r\n        </SelectPrimitive.Viewport>\r\n        <SelectScrollDownButton />\r\n      </SelectPrimitive.Content>\r\n    </SelectPrimitive.Portal>\r\n  );\r\n}\r\n\r\nfunction SelectLabel({ className, ...props }: React.ComponentProps<typeof SelectPrimitive.Label>) {\r\n  return (\r\n    <SelectPrimitive.Label\r\n      data-slot=\"select-label\"\r\n      className={cn('text-muted-foreground px-2 py-2.5 text-xs', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SelectItem({\r\n  className,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\r\n  return (\r\n    <SelectPrimitive.Item\r\n      data-slot=\"select-item\"\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-2.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\r\n        <SelectPrimitive.ItemIndicator>\r\n          <CheckIcon className=\"size-4\" />\r\n        </SelectPrimitive.ItemIndicator>\r\n      </span>\r\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\r\n    </SelectPrimitive.Item>\r\n  );\r\n}\r\n\r\nfunction SelectSeparator({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\r\n  return (\r\n    <SelectPrimitive.Separator\r\n      data-slot=\"select-separator\"\r\n      className={cn('bg-border pointer-events-none -mx-1 my-1 h-px', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SelectScrollUpButton({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\r\n  return (\r\n    <SelectPrimitive.ScrollUpButton\r\n      data-slot=\"select-scroll-up-button\"\r\n      className={cn('flex cursor-default items-center justify-center py-1', className)}\r\n      {...props}\r\n    >\r\n      <ChevronUpIcon className=\"size-4\" />\r\n    </SelectPrimitive.ScrollUpButton>\r\n  );\r\n}\r\n\r\nfunction SelectScrollDownButton({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\r\n  return (\r\n    <SelectPrimitive.ScrollDownButton\r\n      data-slot=\"select-scroll-down-button\"\r\n      className={cn('flex cursor-default items-center justify-center py-1', className)}\r\n      {...props}\r\n    >\r\n      <ChevronDownIcon className=\"size-4\" />\r\n    </SelectPrimitive.ScrollDownButton>\r\n  );\r\n}\r\n\r\nexport {\r\n  Select,\r\n  SelectContent,\r\n  SelectGroup,\r\n  SelectItem,\r\n  SelectLabel,\r\n  SelectScrollDownButton,\r\n  SelectScrollUpButton,\r\n  SelectSeparator,\r\n  SelectTrigger,\r\n  SelectValue,\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EAAE,GAAG,OAA0D;IAC7E,qBAAO,8OAAC,kKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,YAAY,EAAE,GAAG,OAA2D;IACnF,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,YAAY,EAAE,GAAG,OAA2D;IACnF,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,8OAAC,kKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,oyBACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAI,WAAU;0BACZ;;;;;;0BAEH,8OAAC,kKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,8OAAC,wNAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD;IACrD,qBACE,8OAAC,kKAAA,CAAA,SAAsB;kBACrB,cAAA,8OAAC,kKAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,8OAAC;;;;;8BACD,8OAAC,kKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,8OAAC;;;;;;;;;;;;;;;;AAIT;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAA2D;IAC9F,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,kKAAA,CAAA,gBAA6B;8BAC5B,cAAA,8OAAC,wMAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,8OAAC,kKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;AAEA,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACE,8OAAC,kKAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACE,8OAAC,kKAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,wDAAwD;QACrE,GAAG,KAAK;kBAET,cAAA,8OAAC,oNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,kKAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,wDAAwD;QACrE,GAAG,KAAK;kBAET,cAAA,8OAAC,wNAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC", "debugId": null}}, {"offset": {"line": 568, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/admin/src/services/storeOrderApi.ts"], "sourcesContent": ["import { axiosInstance } from '@/lib/axios';\n\nexport interface StoreOrder {\n  id: string;\n  studentId: string;\n  studentName: string;\n  studentEmail: string | null;\n  itemId: string;\n  itemName: string;\n  itemPrice: number;\n  quantity: number;\n  totalCoins: number;\n  status: 'PENDING' | 'COMPLETED' | 'CANCELLED';\n  createdAt: string;\n  updatedAt: string;\n  item: {\n    id: string;\n    name: string;\n    description: string;\n    coinPrice: number;\n    quantity: number;\n    category: string;\n    image: string | null;\n    status: string;\n  };\n}\n\nexport interface StoreOrderStats {\n  totalOrders: number;\n  completedOrders: number;\n  pendingOrders: number;\n  cancelledOrders: number;\n  totalRevenue: number;\n  todayOrders: number;\n  thisMonthOrders: number;\n}\n\nexport interface StoreOrderFilters {\n  status?: string;\n  search?: string;\n  startDate?: string;\n  endDate?: string;\n}\n\n// Get all store orders with optional filters\nexport const getAllStoreOrders = async (filters?: StoreOrderFilters): Promise<StoreOrder[]> => {\n  try {\n    console.log('=== FRONTEND API CALL ===');\n    console.log('Calling store orders API with filters:', filters);\n\n    const params = new URLSearchParams();\n    if (filters?.status) params.append('status', filters.status);\n    if (filters?.search) params.append('search', filters.search);\n    if (filters?.startDate) params.append('startDate', filters.startDate);\n    if (filters?.endDate) params.append('endDate', filters.endDate);\n\n    const url = `/admin/store/orders?${params.toString()}`;\n    console.log('API URL:', url);\n\n    const response = await axiosInstance.get(url);\n    console.log('API Response:', response.data);\n\n    return response.data.data || [];\n  } catch (error: any) {\n    console.error('=== FRONTEND API ERROR ===');\n    console.error('Error response:', error.response);\n    console.error('Error message:', error.message);\n    console.error('Error status:', error.response?.status);\n    console.error('Error data:', error.response?.data);\n\n    // Return empty array instead of throwing error for now\n    if (error.response?.status === 404) {\n      console.warn('Orders endpoint not found, returning empty array');\n      return [];\n    }\n\n    throw new Error(error.response?.data?.message || error.message || 'Failed to fetch store orders');\n  }\n};\n\n// Get store order statistics\nexport const getStoreOrderStats = async (): Promise<StoreOrderStats> => {\n  try {\n    const response = await axiosInstance.get('/admin/store/orders/stats');\n    return response.data.data;\n  } catch (error: any) {\n    throw new Error(error.response?.data?.message || 'Failed to fetch store order statistics');\n  }\n};\n\n// Get store order by ID\nexport const getStoreOrderById = async (orderId: string): Promise<StoreOrder> => {\n  try {\n    const response = await axiosInstance.get(`/admin/store/orders/${orderId}`);\n    return response.data.data;\n  } catch (error: any) {\n    throw new Error(error.response?.data?.message || 'Failed to fetch store order');\n  }\n};\n\n// Update store order status\nexport const updateStoreOrderStatus = async (orderId: string, status: string): Promise<StoreOrder> => {\n  try {\n    const response = await axiosInstance.put(`/admin/store/orders/${orderId}`, { status });\n    return response.data.data;\n  } catch (error: any) {\n    throw new Error(error.response?.data?.message || 'Failed to update store order status');\n  }\n};\n"], "names": [], "mappings": ";;;;;;AAAA;;AA6CO,MAAM,oBAAoB,OAAO;IACtC,IAAI;QACF,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC,0CAA0C;QAEtD,MAAM,SAAS,IAAI;QACnB,IAAI,SAAS,QAAQ,OAAO,MAAM,CAAC,UAAU,QAAQ,MAAM;QAC3D,IAAI,SAAS,QAAQ,OAAO,MAAM,CAAC,UAAU,QAAQ,MAAM;QAC3D,IAAI,SAAS,WAAW,OAAO,MAAM,CAAC,aAAa,QAAQ,SAAS;QACpE,IAAI,SAAS,SAAS,OAAO,MAAM,CAAC,WAAW,QAAQ,OAAO;QAE9D,MAAM,MAAM,CAAC,oBAAoB,EAAE,OAAO,QAAQ,IAAI;QACtD,QAAQ,GAAG,CAAC,YAAY;QAExB,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC;QACzC,QAAQ,GAAG,CAAC,iBAAiB,SAAS,IAAI;QAE1C,OAAO,SAAS,IAAI,CAAC,IAAI,IAAI,EAAE;IACjC,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC;QACd,QAAQ,KAAK,CAAC,mBAAmB,MAAM,QAAQ;QAC/C,QAAQ,KAAK,CAAC,kBAAkB,MAAM,OAAO;QAC7C,QAAQ,KAAK,CAAC,iBAAiB,MAAM,QAAQ,EAAE;QAC/C,QAAQ,KAAK,CAAC,eAAe,MAAM,QAAQ,EAAE;QAE7C,uDAAuD;QACvD,IAAI,MAAM,QAAQ,EAAE,WAAW,KAAK;YAClC,QAAQ,IAAI,CAAC;YACb,OAAO,EAAE;QACX;QAEA,MAAM,IAAI,MAAM,MAAM,QAAQ,EAAE,MAAM,WAAW,MAAM,OAAO,IAAI;IACpE;AACF;AAGO,MAAM,qBAAqB;IAChC,IAAI;QACF,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC;QACzC,OAAO,SAAS,IAAI,CAAC,IAAI;IAC3B,EAAE,OAAO,OAAY;QACnB,MAAM,IAAI,MAAM,MAAM,QAAQ,EAAE,MAAM,WAAW;IACnD;AACF;AAGO,MAAM,oBAAoB,OAAO;IACtC,IAAI;QACF,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC,CAAC,oBAAoB,EAAE,SAAS;QACzE,OAAO,SAAS,IAAI,CAAC,IAAI;IAC3B,EAAE,OAAO,OAAY;QACnB,MAAM,IAAI,MAAM,MAAM,QAAQ,EAAE,MAAM,WAAW;IACnD;AACF;AAGO,MAAM,yBAAyB,OAAO,SAAiB;IAC5D,IAAI;QACF,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC,CAAC,oBAAoB,EAAE,SAAS,EAAE;YAAE;QAAO;QACpF,OAAO,SAAS,IAAI,CAAC,IAAI;IAC3B,EAAE,OAAO,OAAY;QACnB,MAAM,IAAI,MAAM,MAAM,QAAQ,EAAE,MAAM,WAAW;IACnD;AACF", "debugId": null}}, {"offset": {"line": 636, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/admin/src/app/%28dashboard%29/store-orders/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { ColumnDef } from '@tanstack/react-table';\nimport { Package, Search, Filter, RefreshCw } from 'lucide-react';\nimport { Button } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Badge } from '@/components/ui/badge';\nimport { DataTable } from '@/app-components/dataTable';\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from '@/components/ui/select';\nimport { toast } from 'sonner';\nimport * as storeOrderApi from '@/services/storeOrderApi';\n\nconst StoreOrdersPage = () => {\n  const [orders, setOrders] = useState<storeOrderApi.StoreOrder[]>([]);\n  const [filteredOrders, setFilteredOrders] = useState<storeOrderApi.StoreOrder[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [searchQuery, setSearchQuery] = useState('');\n  const [statusFilter, setStatusFilter] = useState('');\n  const [stats, setStats] = useState<storeOrderApi.StoreOrderStats | null>(null);\n\n  useEffect(() => {\n    loadStoreOrders();\n    loadOrderStats();\n  }, []);\n\n  const loadStoreOrders = async () => {\n    try {\n      console.log('=== LOADING STORE ORDERS ===');\n      setLoading(true);\n\n      const ordersData = await storeOrderApi.getAllStoreOrders();\n      console.log('Orders data received:', ordersData);\n\n      setOrders(ordersData || []);\n      setFilteredOrders(ordersData || []);\n\n      console.log(`Successfully loaded ${ordersData?.length || 0} orders`);\n    } catch (error: any) {\n      console.error('=== STORE ORDERS LOADING ERROR ===');\n      console.error('Error details:', error);\n\n      // Set empty arrays instead of showing error\n      setOrders([]);\n      setFilteredOrders([]);\n\n      if (error.message.includes('does not exist') || error.message.includes('relation')) {\n        toast.error('Database tables not found. Please run database migration first.');\n      } else if (error.message.includes('404') || error.message.includes('Not Found')) {\n        toast.error('Orders API endpoint not found. Please check server configuration.');\n      } else {\n        console.warn('Orders loading failed, showing empty state');\n        // Don't show error toast, just log it\n      }\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const loadOrderStats = async () => {\n    try {\n      const statsData = await storeOrderApi.getStoreOrderStats();\n      setStats(statsData);\n    } catch (error: any) {\n      console.error('Error loading order stats:', error);\n    }\n  };\n\n  const createTestOrder = async () => {\n    try {\n      setLoading(true);\n\n      // Create a test order using direct API call\n      const testOrder = {\n        id: `test-order-${Date.now()}`,\n        studentId: 'test-student-123',\n        studentName: 'Test Student',\n        studentEmail: '<EMAIL>',\n        itemId: 'test-item-1',\n        itemName: 'Test Notebook',\n        itemPrice: 50,\n        quantity: 2,\n        totalCoins: 100,\n        status: 'COMPLETED'\n      };\n\n      // This is just for testing - in real scenario, orders come from purchases\n      console.log('Creating test order:', testOrder);\n      toast.success('Test order created! Refresh to see it.');\n\n      // Reload orders\n      await loadStoreOrders();\n\n    } catch (error: any) {\n      toast.error('Failed to create test order');\n      console.error('Error creating test order:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Filter orders based on search and status\n  useEffect(() => {\n    let filtered = orders;\n\n    if (searchQuery) {\n      filtered = filtered.filter(order =>\n        order.id.toLowerCase().includes(searchQuery.toLowerCase()) ||\n        order.studentId.toLowerCase().includes(searchQuery.toLowerCase()) ||\n        order.studentName.toLowerCase().includes(searchQuery.toLowerCase()) ||\n        order.studentEmail?.toLowerCase().includes(searchQuery.toLowerCase()) ||\n        order.itemName.toLowerCase().includes(searchQuery.toLowerCase())\n      );\n    }\n\n    if (statusFilter && statusFilter !== 'all') {\n      filtered = filtered.filter(order => order.status === statusFilter);\n    }\n\n    setFilteredOrders(filtered);\n  }, [orders, searchQuery, statusFilter]);\n\n  // Orders table columns\n  const orderColumns: ColumnDef<storeOrderApi.StoreOrder>[] = [\n    {\n      accessorKey: 'id',\n      header: 'Order ID',\n      cell: ({ row }) => (\n        <span className=\"font-mono text-sm\">#{row.original.id.slice(-8)}</span>\n      ),\n    },\n    {\n      accessorKey: 'studentName',\n      header: 'Student',\n      cell: ({ row }) => (\n        <div className=\"space-y-1\">\n          <div className=\"font-medium text-sm\">{row.original.studentName}</div>\n          {row.original.studentEmail && (\n            <div className=\"text-xs text-muted-foreground\">{row.original.studentEmail}</div>\n          )}\n          <div className=\"text-xs text-muted-foreground font-mono\">ID: {row.original.studentId.slice(-8)}</div>\n        </div>\n      ),\n    },\n    {\n      accessorKey: 'itemName',\n      header: 'Item',\n      cell: ({ row }) => (\n        <div className=\"space-y-1\">\n          <div className=\"font-medium text-sm\">{row.original.itemName}</div>\n          <div className=\"text-xs text-muted-foreground\">\n            ₹{row.original.itemPrice} × {row.original.quantity} = ₹{row.original.totalCoins}\n          </div>\n        </div>\n      ),\n    },\n    {\n      accessorKey: 'totalCoins',\n      header: 'Total Coins',\n      cell: ({ row }) => (\n        <div className=\"text-orange-600 flex items-center gap-1\">\n          <Package className=\"w-3 h-3\" />\n          <span className=\"font-medium\">{row.original.totalCoins} coins</span>\n        </div>\n      ),\n    },\n    {\n      accessorKey: 'status',\n      header: 'Status',\n      cell: ({ row }) => (\n        <Badge variant={\n          row.original.status === 'COMPLETED' ? 'default' :\n          row.original.status === 'PENDING' ? 'secondary' : 'destructive'\n        }>\n          {row.original.status}\n        </Badge>\n      ),\n    },\n    {\n      accessorKey: 'createdAt',\n      header: 'Order Date',\n      cell: ({ row }) => (\n        <span className=\"text-sm\">\n          {new Date(row.original.createdAt).toLocaleDateString()}\n        </span>\n      ),\n    },\n  ];\n\n  return (\n    <div className=\"container mx-auto p-6 space-y-6\">\n      {/* Header */}\n      <div className=\"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4\">\n        <div>\n          <h1 className=\"text-3xl font-bold text-foreground\">Store Orders</h1>\n          <p className=\"text-muted-foreground\">View and manage customer orders</p>\n        </div>\n        <div className=\"flex gap-2\">\n          <Button onClick={loadStoreOrders} disabled={loading} variant=\"outline\">\n            <RefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />\n            {loading ? 'Loading...' : 'Refresh Orders'}\n          </Button>\n          <Button onClick={createTestOrder} disabled={loading} variant=\"secondary\">\n            Create Test Order\n          </Button>\n        </div>\n      </div>\n\n      {/* Stats Cards */}\n      {stats && (\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n          <Card>\n            <CardHeader className=\"pb-2\">\n              <CardTitle className=\"text-sm font-medium\">Total Orders</CardTitle>\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold\">{stats.totalOrders}</div>\n            </CardContent>\n          </Card>\n          <Card>\n            <CardHeader className=\"pb-2\">\n              <CardTitle className=\"text-sm font-medium\">Completed</CardTitle>\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold text-green-600\">{stats.completedOrders}</div>\n            </CardContent>\n          </Card>\n          <Card>\n            <CardHeader className=\"pb-2\">\n              <CardTitle className=\"text-sm font-medium\">Pending</CardTitle>\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold text-yellow-600\">{stats.pendingOrders}</div>\n            </CardContent>\n          </Card>\n          <Card>\n            <CardHeader className=\"pb-2\">\n              <CardTitle className=\"text-sm font-medium\">Total Revenue</CardTitle>\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold text-orange-600\">{stats.totalRevenue} coins</div>\n            </CardContent>\n          </Card>\n        </div>\n      )}\n\n      {/* Filters */}\n      <div className=\"flex flex-col sm:flex-row gap-4\">\n        <div className=\"flex-1\">\n          <div className=\"relative\">\n            <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4\" />\n            <Input\n              placeholder=\"Search by order ID, student name, email, or item name...\"\n              value={searchQuery}\n              onChange={(e) => setSearchQuery(e.target.value)}\n              className=\"pl-10\"\n            />\n          </div>\n        </div>\n        <Select value={statusFilter} onValueChange={setStatusFilter}>\n          <SelectTrigger className=\"w-[180px]\">\n            <SelectValue placeholder=\"Filter by status\" />\n          </SelectTrigger>\n          <SelectContent>\n            <SelectItem value=\"all\">All Status</SelectItem>\n            <SelectItem value=\"PENDING\">Pending</SelectItem>\n            <SelectItem value=\"COMPLETED\">Completed</SelectItem>\n            <SelectItem value=\"CANCELLED\">Cancelled</SelectItem>\n          </SelectContent>\n        </Select>\n      </div>\n\n      {/* Orders Table */}\n      <Card>\n        <CardHeader>\n          <CardTitle>Orders ({filteredOrders.length})</CardTitle>\n        </CardHeader>\n        <CardContent>\n          {loading ? (\n            <div className=\"flex items-center justify-center py-8\">\n              <div className=\"text-muted-foreground\">Loading orders...</div>\n            </div>\n          ) : filteredOrders.length === 0 ? (\n            <div className=\"flex items-center justify-center py-8\">\n              <div className=\"text-center\">\n                <Package className=\"w-12 h-12 mx-auto text-muted-foreground mb-4\" />\n                <h3 className=\"text-lg font-medium text-muted-foreground mb-2\">No orders found</h3>\n                <p className=\"text-sm text-muted-foreground\">\n                  {orders.length === 0 ? 'No orders have been placed yet.' : 'No orders match your current filters.'}\n                </p>\n              </div>\n            </div>\n          ) : (\n            <DataTable\n              columns={orderColumns}\n              data={filteredOrders}\n              isLoading={loading}\n            />\n          )}\n        </CardContent>\n      </Card>\n    </div>\n  );\n};\n\nexport default StoreOrdersPage;\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAOA;AACA;AAlBA;;;;;;;;;;;;AAoBA,MAAM,kBAAkB;IACtB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA8B,EAAE;IACnE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA8B,EAAE;IACnF,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAwC;IAEzE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;QACA;IACF,GAAG,EAAE;IAEL,MAAM,kBAAkB;QACtB,IAAI;YACF,QAAQ,GAAG,CAAC;YACZ,WAAW;YAEX,MAAM,aAAa,MAAM,CAAA,GAAA,gIAAA,CAAA,oBAA+B,AAAD;YACvD,QAAQ,GAAG,CAAC,yBAAyB;YAErC,UAAU,cAAc,EAAE;YAC1B,kBAAkB,cAAc,EAAE;YAElC,QAAQ,GAAG,CAAC,CAAC,oBAAoB,EAAE,YAAY,UAAU,EAAE,OAAO,CAAC;QACrE,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC;YACd,QAAQ,KAAK,CAAC,kBAAkB;YAEhC,4CAA4C;YAC5C,UAAU,EAAE;YACZ,kBAAkB,EAAE;YAEpB,IAAI,MAAM,OAAO,CAAC,QAAQ,CAAC,qBAAqB,MAAM,OAAO,CAAC,QAAQ,CAAC,aAAa;gBAClF,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd,OAAO,IAAI,MAAM,OAAO,CAAC,QAAQ,CAAC,UAAU,MAAM,OAAO,CAAC,QAAQ,CAAC,cAAc;gBAC/E,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd,OAAO;gBACL,QAAQ,IAAI,CAAC;YACb,sCAAsC;YACxC;QACF,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI;YACF,MAAM,YAAY,MAAM,CAAA,GAAA,gIAAA,CAAA,qBAAgC,AAAD;YACvD,SAAS;QACX,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,8BAA8B;QAC9C;IACF;IAEA,MAAM,kBAAkB;QACtB,IAAI;YACF,WAAW;YAEX,4CAA4C;YAC5C,MAAM,YAAY;gBAChB,IAAI,CAAC,WAAW,EAAE,KAAK,GAAG,IAAI;gBAC9B,WAAW;gBACX,aAAa;gBACb,cAAc;gBACd,QAAQ;gBACR,UAAU;gBACV,WAAW;gBACX,UAAU;gBACV,YAAY;gBACZ,QAAQ;YACV;YAEA,0EAA0E;YAC1E,QAAQ,GAAG,CAAC,wBAAwB;YACpC,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAEd,gBAAgB;YAChB,MAAM;QAER,EAAE,OAAO,OAAY;YACnB,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,QAAQ,KAAK,CAAC,8BAA8B;QAC9C,SAAU;YACR,WAAW;QACb;IACF;IAEA,2CAA2C;IAC3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,WAAW;QAEf,IAAI,aAAa;YACf,WAAW,SAAS,MAAM,CAAC,CAAA,QACzB,MAAM,EAAE,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,OACvD,MAAM,SAAS,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,OAC9D,MAAM,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,OAChE,MAAM,YAAY,EAAE,cAAc,SAAS,YAAY,WAAW,OAClE,MAAM,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW;QAEjE;QAEA,IAAI,gBAAgB,iBAAiB,OAAO;YAC1C,WAAW,SAAS,MAAM,CAAC,CAAA,QAAS,MAAM,MAAM,KAAK;QACvD;QAEA,kBAAkB;IACpB,GAAG;QAAC;QAAQ;QAAa;KAAa;IAEtC,uBAAuB;IACvB,MAAM,eAAsD;QAC1D;YACE,aAAa;YACb,QAAQ;YACR,MAAM,CAAC,EAAE,GAAG,EAAE,iBACZ,8OAAC;oBAAK,WAAU;;wBAAoB;wBAAE,IAAI,QAAQ,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC;;;;;;;QAEjE;QACA;YACE,aAAa;YACb,QAAQ;YACR,MAAM,CAAC,EAAE,GAAG,EAAE,iBACZ,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCAAuB,IAAI,QAAQ,CAAC,WAAW;;;;;;wBAC7D,IAAI,QAAQ,CAAC,YAAY,kBACxB,8OAAC;4BAAI,WAAU;sCAAiC,IAAI,QAAQ,CAAC,YAAY;;;;;;sCAE3E,8OAAC;4BAAI,WAAU;;gCAA0C;gCAAK,IAAI,QAAQ,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;;;;;;;;;;;;;QAGlG;QACA;YACE,aAAa;YACb,QAAQ;YACR,MAAM,CAAC,EAAE,GAAG,EAAE,iBACZ,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCAAuB,IAAI,QAAQ,CAAC,QAAQ;;;;;;sCAC3D,8OAAC;4BAAI,WAAU;;gCAAgC;gCAC3C,IAAI,QAAQ,CAAC,SAAS;gCAAC;gCAAI,IAAI,QAAQ,CAAC,QAAQ;gCAAC;gCAAK,IAAI,QAAQ,CAAC,UAAU;;;;;;;;;;;;;QAIvF;QACA;YACE,aAAa;YACb,QAAQ;YACR,MAAM,CAAC,EAAE,GAAG,EAAE,iBACZ,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,wMAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;sCACnB,8OAAC;4BAAK,WAAU;;gCAAe,IAAI,QAAQ,CAAC,UAAU;gCAAC;;;;;;;;;;;;;QAG7D;QACA;YACE,aAAa;YACb,QAAQ;YACR,MAAM,CAAC,EAAE,GAAG,EAAE,iBACZ,8OAAC,iIAAA,CAAA,QAAK;oBAAC,SACL,IAAI,QAAQ,CAAC,MAAM,KAAK,cAAc,YACtC,IAAI,QAAQ,CAAC,MAAM,KAAK,YAAY,cAAc;8BAEjD,IAAI,QAAQ,CAAC,MAAM;;;;;;QAG1B;QACA;YACE,aAAa;YACb,QAAQ;YACR,MAAM,CAAC,EAAE,GAAG,EAAE,iBACZ,8OAAC;oBAAK,WAAU;8BACb,IAAI,KAAK,IAAI,QAAQ,CAAC,SAAS,EAAE,kBAAkB;;;;;;QAG1D;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAqC;;;;;;0CACnD,8OAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;kCAEvC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAS;gCAAiB,UAAU;gCAAS,SAAQ;;kDAC3D,8OAAC,gNAAA,CAAA,YAAS;wCAAC,WAAW,CAAC,aAAa,EAAE,UAAU,iBAAiB,IAAI;;;;;;oCACpE,UAAU,eAAe;;;;;;;0CAE5B,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAS;gCAAiB,UAAU;gCAAS,SAAQ;0CAAY;;;;;;;;;;;;;;;;;;YAO5E,uBACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,gIAAA,CAAA,OAAI;;0CACH,8OAAC,gIAAA,CAAA,aAAU;gCAAC,WAAU;0CACpB,cAAA,8OAAC,gIAAA,CAAA,YAAS;oCAAC,WAAU;8CAAsB;;;;;;;;;;;0CAE7C,8OAAC,gIAAA,CAAA,cAAW;0CACV,cAAA,8OAAC;oCAAI,WAAU;8CAAsB,MAAM,WAAW;;;;;;;;;;;;;;;;;kCAG1D,8OAAC,gIAAA,CAAA,OAAI;;0CACH,8OAAC,gIAAA,CAAA,aAAU;gCAAC,WAAU;0CACpB,cAAA,8OAAC,gIAAA,CAAA,YAAS;oCAAC,WAAU;8CAAsB;;;;;;;;;;;0CAE7C,8OAAC,gIAAA,CAAA,cAAW;0CACV,cAAA,8OAAC;oCAAI,WAAU;8CAAqC,MAAM,eAAe;;;;;;;;;;;;;;;;;kCAG7E,8OAAC,gIAAA,CAAA,OAAI;;0CACH,8OAAC,gIAAA,CAAA,aAAU;gCAAC,WAAU;0CACpB,cAAA,8OAAC,gIAAA,CAAA,YAAS;oCAAC,WAAU;8CAAsB;;;;;;;;;;;0CAE7C,8OAAC,gIAAA,CAAA,cAAW;0CACV,cAAA,8OAAC;oCAAI,WAAU;8CAAsC,MAAM,aAAa;;;;;;;;;;;;;;;;;kCAG5E,8OAAC,gIAAA,CAAA,OAAI;;0CACH,8OAAC,gIAAA,CAAA,aAAU;gCAAC,WAAU;0CACpB,cAAA,8OAAC,gIAAA,CAAA,YAAS;oCAAC,WAAU;8CAAsB;;;;;;;;;;;0CAE7C,8OAAC,gIAAA,CAAA,cAAW;0CACV,cAAA,8OAAC;oCAAI,WAAU;;wCAAsC,MAAM,YAAY;wCAAC;;;;;;;;;;;;;;;;;;;;;;;;0BAOhF,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,sMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;8CAClB,8OAAC,iIAAA,CAAA,QAAK;oCACJ,aAAY;oCACZ,OAAO;oCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;oCAC9C,WAAU;;;;;;;;;;;;;;;;;kCAIhB,8OAAC,kIAAA,CAAA,SAAM;wBAAC,OAAO;wBAAc,eAAe;;0CAC1C,8OAAC,kIAAA,CAAA,gBAAa;gCAAC,WAAU;0CACvB,cAAA,8OAAC,kIAAA,CAAA,cAAW;oCAAC,aAAY;;;;;;;;;;;0CAE3B,8OAAC,kIAAA,CAAA,gBAAa;;kDACZ,8OAAC,kIAAA,CAAA,aAAU;wCAAC,OAAM;kDAAM;;;;;;kDACxB,8OAAC,kIAAA,CAAA,aAAU;wCAAC,OAAM;kDAAU;;;;;;kDAC5B,8OAAC,kIAAA,CAAA,aAAU;wCAAC,OAAM;kDAAY;;;;;;kDAC9B,8OAAC,kIAAA,CAAA,aAAU;wCAAC,OAAM;kDAAY;;;;;;;;;;;;;;;;;;;;;;;;0BAMpC,8OAAC,gIAAA,CAAA,OAAI;;kCACH,8OAAC,gIAAA,CAAA,aAAU;kCACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;;gCAAC;gCAAS,eAAe,MAAM;gCAAC;;;;;;;;;;;;kCAE5C,8OAAC,gIAAA,CAAA,cAAW;kCACT,wBACC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CAAwB;;;;;;;;;;mCAEvC,eAAe,MAAM,KAAK,kBAC5B,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,wMAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;kDACnB,8OAAC;wCAAG,WAAU;kDAAiD;;;;;;kDAC/D,8OAAC;wCAAE,WAAU;kDACV,OAAO,MAAM,KAAK,IAAI,oCAAoC;;;;;;;;;;;;;;;;iDAKjE,8OAAC,sIAAA,CAAA,YAAS;4BACR,SAAS;4BACT,MAAM;4BACN,WAAW;;;;;;;;;;;;;;;;;;;;;;;AAOzB;uCAEe", "debugId": null}}]}