import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcrypt';

const prisma = new PrismaClient();

async function main() {
  const hashedPassword = await bcrypt.hash('admin@123', 10);

  const admin = await prisma.adminUser.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      password: hashedPassword,
    },
  });

  console.log('Admin user created:', admin);

  // Create test student
  const testStudent = await prisma.student.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      firstName: 'Test',
      lastName: 'Student',
      email: '<EMAIL>',
      contact: '1234567890',
      isVerified: true,
    },
  });

  console.log('Test student created:', testStudent);

  // Create test store items
  const testItem1 = await prisma.storeItem.upsert({
    where: { id: 'test-item-1' },
    update: {},
    create: {
      id: 'test-item-1',
      name: 'Test Notebook',
      description: 'A high-quality notebook for students',
      coinPrice: 50,
      quantity: 100,
      category: 'Stationery',
      status: 'ACTIVE',
    },
  });

  const testItem2 = await prisma.storeItem.upsert({
    where: { id: 'test-item-2' },
    update: {},
    create: {
      id: 'test-item-2',
      name: 'Test Pen Set',
      description: 'Set of 5 premium pens',
      coinPrice: 30,
      quantity: 50,
      category: 'Stationery',
      status: 'ACTIVE',
    },
  });

  console.log('Test store items created:', { testItem1, testItem2 });

  // Create test orders
  const testOrder1 = await prisma.storeOrder.create({
    data: {
      studentId: testStudent.id,
      studentName: `${testStudent.firstName} ${testStudent.lastName}`,
      studentEmail: testStudent.email,
      itemId: testItem1.id,
      itemName: testItem1.name,
      itemPrice: testItem1.coinPrice,
      quantity: 2,
      totalCoins: testItem1.coinPrice * 2,
      status: 'COMPLETED',
    },
  });

  const testOrder2 = await prisma.storeOrder.create({
    data: {
      studentId: testStudent.id,
      studentName: `${testStudent.firstName} ${testStudent.lastName}`,
      studentEmail: testStudent.email,
      itemId: testItem2.id,
      itemName: testItem2.name,
      itemPrice: testItem2.coinPrice,
      quantity: 1,
      totalCoins: testItem2.coinPrice * 1,
      status: 'COMPLETED',
    },
  });

  console.log('Test orders created:', { testOrder1, testOrder2 });
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(() => {
    prisma.$disconnect();
  });
