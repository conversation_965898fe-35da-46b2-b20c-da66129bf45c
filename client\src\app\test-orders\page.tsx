'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { toast } from 'sonner';
import * as storePurchaseApi from '@/services/storePurchaseApi';

const TestOrdersPage = () => {
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<any>(null);

  const testOrdersAPI = async () => {
    try {
      setLoading(true);
      setResult(null);
      
      console.log('=== TESTING ORDERS API ===');
      
      // Test the API call
      const orders = await storePurchaseApi.getMyOrders();
      
      console.log('API Response:', orders);
      
      setResult({
        success: true,
        data: orders,
        message: `Successfully fetched ${orders.length} orders`
      });
      
      toast.success(`API test successful! Found ${orders.length} orders`);
      
    } catch (error: any) {
      console.error('=== API TEST ERROR ===');
      console.error('Error details:', error);
      
      setResult({
        success: false,
        error: error.message,
        details: error
      });
      
      toast.error(`API test failed: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Test Orders API</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <Button 
            onClick={testOrdersAPI} 
            disabled={loading}
            className="w-full"
          >
            {loading ? 'Testing...' : 'Test Orders API'}
          </Button>
          
          {result && (
            <Card>
              <CardHeader>
                <CardTitle className={result.success ? 'text-green-600' : 'text-red-600'}>
                  {result.success ? 'Success' : 'Error'}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto">
                  {JSON.stringify(result, null, 2)}
                </pre>
              </CardContent>
            </Card>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default TestOrdersPage;
