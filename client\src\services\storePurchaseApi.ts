import { axiosInstance } from '@/lib/axios';

export interface CartItem {
  id: string;
  name: string;
  coinPrice: number;
  quantity: number;
  image: string;
}

export interface PurchaseData {
  cartItems: CartItem[];
  totalCoins: number;
}

export interface PurchaseResponse {
  success: boolean;
  orderId: string;
  message: string;
}

export interface StoreOrder {
  id: string;
  studentId: string;
  studentName: string;
  studentEmail: string | null;
  itemId: string;
  itemName: string;
  itemPrice: number;
  quantity: number;
  totalCoins: number;
  status: 'PENDING' | 'COMPLETED' | 'CANCELLED';
  createdAt: string;
  updatedAt: string;
  item: {
    id: string;
    name: string;
    description: string;
    coinPrice: number;
    quantity: number;
    category: string;
    image: string | null;
    status: string;
  };
}



// Purchase items from store
export const purchaseItems = async (data: PurchaseData): Promise<PurchaseResponse> => {
  try {
    const response = await axiosInstance.post('/store/purchase', data);
    return response.data.data;
  } catch (error: any) {
    throw new Error(error.response?.data?.message || 'Purchase failed');
  }
};

// Get student's orders
export const getMyOrders = async (): Promise<StoreOrder[]> => {
  try {
    console.log('=== FETCHING STUDENT ORDERS ===');
    const response = await axiosInstance.get('/store/orders');
    console.log('Orders API response:', response.data);
    return response.data.data || [];
  } catch (error: any) {
    console.error('=== ORDERS API ERROR ===');
    console.error('Error details:', error.response?.data || error.message);
    throw new Error(error.response?.data?.message || 'Failed to fetch orders');
  }
};

// Get specific order details
export const getOrderDetails = async (orderId: string): Promise<StoreOrder> => {
  try {
    const response = await axiosInstance.get(`/store/orders/${orderId}`);
    return response.data.data;
  } catch (error: any) {
    throw new Error(error.response?.data?.message || 'Failed to fetch order details');
  }
};
