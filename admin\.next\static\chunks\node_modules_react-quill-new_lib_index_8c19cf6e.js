(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["static/chunks/node_modules_react-quill-new_lib_index_8c19cf6e.js", {

"[project]/node_modules/react-quill-new/lib/index.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_84969839._.js",
  "static/chunks/node_modules_react-quill-new_lib_index_33497035.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/react-quill-new/lib/index.js [app-client] (ecmascript)");
    });
});
}}),
}]);