import { axiosInstance } from '@/lib/axios';

export interface StoreItem {
  id: string;
  name: string;
  description: string;
  coinPrice: number;
  quantity: number;
  category: string;
  image: string | null;
  status: 'ACTIVE' | 'INACTIVE';
  createdAt: string;
  updatedAt: string;
}

export interface StoreFilters {
  category?: string;
  status?: string;
  search?: string;
}

export interface StoreStats {
  totalItems: number;
  activeItems: number;
  inactiveItems: number;
  outOfStockItems: number;
  categoriesCount: number;
  categories: Array<{
    category: string;
    count: number;
  }>;
}

// Get all store items with optional filters (for client)
export const getAllStoreItems = async (filters?: StoreFilters): Promise<StoreItem[]> => {
  try {
    const params = new URLSearchParams();
    if (filters?.category) params.append('category', filters.category);
    if (filters?.status) params.append('status', filters.status);
    if (filters?.search) params.append('search', filters.search);

    // Only show active items for clients
    params.append('status', 'ACTIVE');

    const response = await axiosInstance.get(`/admin/store?${params.toString()}`);
    return response.data.data;
  } catch (error: any) {
    throw new Error(error.response?.data?.message || 'Failed to fetch store items');
  }
};

// Get store item by ID
export const getStoreItemById = async (id: string): Promise<StoreItem> => {
  try {
    const response = await axiosInstance.get(`/admin/store/${id}`);
    return response.data.data;
  } catch (error: any) {
    throw new Error(error.response?.data?.message || 'Failed to fetch store item');
  }
};

// Get store statistics
export const getStoreStats = async (): Promise<StoreStats> => {
  try {
    const response = await axiosInstance.get('/admin/store/stats');
    return response.data.data;
  } catch (error: any) {
    throw new Error(error.response?.data?.message || 'Failed to fetch store statistics');
  }
};
