import prisma from '../../../config/prismaClient';
import { getUestCoins } from './uestCoinTransactionService';

interface CartItem {
  id: string;
  name: string;
  coinPrice: number;
  quantity: number;
}

interface PurchaseData {
  studentId: string;
  cartItems: CartItem[];
  totalCoins: number;
}

export const processPurchase = async (data: PurchaseData) => {
  const { studentId, cartItems, totalCoins } = data;

  try {
    // Start transaction
    const result = await prisma.$transaction(async (tx) => {
      // 1. Check student's coin balance
      const studentCoins = await getUestCoins(studentId, 'STUDENT');
      if (!studentCoins || studentCoins.coins < totalCoins) {
        throw new Error('Insufficient coins balance');
      }

      // 2. Check item availability and stock
      for (const cartItem of cartItems) {
        console.log('Validating cart item:', cartItem);

        const storeItem = await tx.storeItem.findUnique({
          where: { id: cartItem.id }
        });

        console.log('Found store item:', storeItem);

        if (!storeItem) {
          console.error(`Store item not found for ID: ${cartItem.id}`);
          throw new Error(`Store item not found: ${cartItem.name} (ID: ${cartItem.id})`);
        }

        if (storeItem.status !== 'ACTIVE') {
          throw new Error(`Item ${cartItem.name} is not available`);
        }

        if (storeItem.quantity < cartItem.quantity) {
          throw new Error(`Insufficient stock for ${cartItem.name}. Available: ${storeItem.quantity}, Requested: ${cartItem.quantity}`);
        }

        if (storeItem.coinPrice !== cartItem.coinPrice) {
          throw new Error(`Price mismatch for ${cartItem.name}`);
        }
      }

      // 3. Get student details
      const student = await tx.student.findUnique({
        where: { id: studentId }
      });

      if (!student) {
        throw new Error('Student not found');
      }

      // 4. Create separate order for each item and update stock
      const orderIds = [];

      for (const cartItem of cartItems) {
        const itemTotalCoins = cartItem.coinPrice * cartItem.quantity;

        // Create order for this item
        console.log('Creating order with data:', {
          studentId,
          studentName: `${student.firstName} ${student.lastName}`,
          studentEmail: student.email,
          itemId: cartItem.id,
          itemName: cartItem.name,
          itemPrice: cartItem.coinPrice,
          quantity: cartItem.quantity,
          totalCoins: itemTotalCoins,
          status: 'COMPLETED'
        });

        const order = await tx.storeOrder.create({
          data: {
            studentId,
            studentName: `${student.firstName} ${student.lastName}`,
            studentEmail: student.email,
            itemId: cartItem.id,
            itemName: cartItem.name,
            itemPrice: cartItem.coinPrice,
            quantity: cartItem.quantity,
            totalCoins: itemTotalCoins,
            status: 'COMPLETED'
          }
        });

        console.log('Order created successfully:', order);

        orderIds.push(order.id);

        // Update item stock
        await tx.storeItem.update({
          where: { id: cartItem.id },
          data: {
            quantity: {
              decrement: cartItem.quantity
            }
          }
        });
      }

      // 5. Deduct coins from student
      await tx.uestCoins.update({
        where: {
          modelId_modelType: {
            modelId: studentId,
            modelType: 'STUDENT'
          }
        },
        data: {
          coins: {
            decrement: totalCoins
          }
        }
      });

      // 6. Create coin transaction record
      await tx.uestCoinTransaction.create({
        data: {
          modelId: studentId,
          modelType: 'STUDENT',
          amount: totalCoins,
          type: 'DEBIT',
          reason: `Store purchase - ${orderIds.length} items (Order #${orderIds[0].slice(-8)})`
        }
      });

      return { orderIds, firstOrderId: orderIds[0] };
    });

    return {
      success: true,
      orderId: result.firstOrderId,
      orderIds: result.orderIds,
      message: 'Purchase completed successfully'
    };

  } catch (error: any) {
    throw new Error(error.message || 'Purchase failed');
  }
};

export const getStudentOrders = async (studentId: string) => {
  try {
    console.log('=== FETCHING ORDERS FOR STUDENT ===');
    console.log('Student ID:', studentId);

    const orders = await prisma.storeOrder.findMany({
      where: { studentId },
      include: {
        item: true
      },
      orderBy: { createdAt: 'desc' }
    });

    console.log('Found orders:', orders.length);
    console.log('Orders data:', orders);

    return orders;
  } catch (error: any) {
    console.error('=== ORDER FETCH ERROR ===');
    console.error('Error details:', error);
    throw new Error('Failed to fetch orders');
  }
};

export const getOrderById = async (orderId: string, studentId: string) => {
  try {
    const order = await prisma.storeOrder.findFirst({
      where: {
        id: orderId,
        studentId
      },
      include: {
        item: true
      }
    });

    if (!order) {
      throw new Error('Order not found');
    }

    return order;
  } catch (error: any) {
    throw new Error('Failed to fetch order');
  }
};
